import React from 'react';

/**
 * Skeleton Loader Components
 * 
 * Advanced skeleton loaders for different dashboard components
 * with smooth animations and realistic placeholders
 */

// Base skeleton component with shimmer effect
const SkeletonBase = ({ className = "", children, ...props }) => (
  <div 
    className={`animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer ${className}`}
    {...props}
  >
    {children}
  </div>
);

// KPI Card Skeleton
export const DashboardCardSkeleton = () => (
  <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
    <div className="flex items-center justify-between">
      <div className="space-y-3 flex-1">
        <SkeletonBase className="h-4 w-24 rounded" />
        <SkeletonBase className="h-8 w-32 rounded" />
        <SkeletonBase className="h-3 w-20 rounded" />
      </div>
      <SkeletonBase className="w-12 h-12 rounded-lg" />
    </div>
  </div>
);

// Chart Skeleton
export const ChartSkeleton = ({ height = 400, title = "Chart" }) => (
  <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-100">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center">
        <SkeletonBase className="w-1 h-6 rounded-full mr-3" />
        <SkeletonBase className="h-6 w-48 rounded" />
      </div>
      <SkeletonBase className="h-8 w-20 rounded" />
    </div>
    
    <div className="relative" style={{ height: `${height}px` }}>
      {/* Chart area */}
      <SkeletonBase className="w-full h-full rounded-lg" />
      
      {/* Simulated chart elements */}
      <div className="absolute inset-4 flex items-end justify-between">
        {[...Array(7)].map((_, i) => (
          <SkeletonBase 
            key={i}
            className="w-8 rounded-t"
            style={{ height: `${Math.random() * 60 + 20}%` }}
          />
        ))}
      </div>
    </div>
    
    <div className="mt-4 flex justify-center space-x-4">
      <SkeletonBase className="h-3 w-32 rounded" />
      <SkeletonBase className="h-3 w-24 rounded" />
    </div>
  </div>
);

// Payment Method Chart Skeleton
export const PaymentMethodChartSkeleton = () => (
  <div className="bg-white rounded-lg shadow-lg p-6 border border-amber-100">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center">
        <SkeletonBase className="w-1 h-6 bg-amber-300 rounded-full mr-3" />
        <SkeletonBase className="h-6 w-56 rounded" />
      </div>
      <SkeletonBase className="h-8 w-20 rounded" />
    </div>
    
    <div className="flex flex-col xl:flex-row items-start gap-8">
      {/* Pie chart skeleton */}
      <div className="flex-shrink-0 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg p-6 mx-auto">
        <div className="relative w-96 h-80">
          <SkeletonBase className="w-full h-full rounded-full" />
          {/* Center hole for donut chart */}
          <div className="absolute inset-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white rounded-full flex items-center justify-center">
            <div className="text-center">
              <SkeletonBase className="h-6 w-16 rounded mb-2 mx-auto" />
              <SkeletonBase className="h-3 w-20 rounded mx-auto" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Side panel skeleton */}
      <div className="flex-1 space-y-2 min-w-[320px] max-w-[400px]">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <SkeletonBase className="w-4 h-4 rounded-full" />
                <div>
                  <SkeletonBase className="h-4 w-16 rounded mb-1" />
                  <SkeletonBase className="h-3 w-12 rounded" />
                </div>
              </div>
              <div className="text-right">
                <SkeletonBase className="h-5 w-20 rounded mb-1" />
                <div className="flex items-center gap-2">
                  <SkeletonBase className="h-3 w-8 rounded" />
                  <SkeletonBase className="h-4 w-10 rounded" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
    
    <div className="mt-4 space-y-2">
      <SkeletonBase className="h-3 w-3/4 rounded" />
      <SkeletonBase className="h-3 w-2/3 rounded" />
    </div>
  </div>
);

// Filter Section Skeleton
export const FilterSkeleton = () => (
  <div className="bg-white rounded-lg shadow p-4">
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div className="flex items-center">
        <SkeletonBase className="w-5 h-5 rounded mr-2" />
        <SkeletonBase className="h-5 w-16 rounded" />
      </div>
      <div className="flex flex-wrap gap-3 items-center">
        <SkeletonBase className="h-10 w-32 rounded" />
        <SkeletonBase className="h-10 w-40 rounded" />
      </div>
    </div>
  </div>
);

// Progressive Loading Spinner
export const LoadingSpinner = ({ size = "md", color = "blue" }) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12",
    xl: "w-16 h-16"
  };
  
  const colorClasses = {
    blue: "text-blue-600",
    amber: "text-amber-600",
    green: "text-green-600",
    purple: "text-purple-600"
  };

  return (
    <div className="flex items-center justify-center">
      <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-spin`}>
        <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="4"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>
    </div>
  );
};

// Advanced Loading Overlay
export const LoadingOverlay = ({ message = "Loading...", progress = null }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center">
      <LoadingSpinner size="xl" color="blue" />
      <p className="mt-4 text-lg font-medium text-gray-900">{message}</p>
      {progress !== null && (
        <div className="mt-4">
          <div className="bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-sm text-gray-600 mt-2">{progress}% complete</p>
        </div>
      )}
    </div>
  </div>
);

// Pulse Animation for individual elements
export const PulseLoader = ({ className = "" }) => (
  <div className={`animate-pulse bg-gray-300 rounded ${className}`} />
);
