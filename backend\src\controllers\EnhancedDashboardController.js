// backend/src/controllers/EnhancedDashboardController.js
const db = require('../config/database');
const sql = require('mssql');
const redisService = require('../services/RedisService');

/**
 * ===============================================================================
 * # Enhanced Dashboard Controller with Redis Caching
 * ===============================================================================
 *
 * This enhanced controller provides high-performance dashboard endpoints
 * with intelligent Redis caching, real-time data updates, and optimized
 * database queries for the PWVMS parking management system.
 */

/**
 * Helper function to calculate date range based on selection
 * Operational day: 6:00 AM to 5:59 AM (next day)
 */
function calculateDateRange(dateRange) {
  const referenceDate = new Date();
  let startDate, endDate;
  
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date - operational day from 6:00 AM to 5:59 AM next day
    startDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate.setDate(endDate.getDate() + 1); // Next day
    endDate.setSeconds(endDate.getSeconds() - 1); // 5:59:59 AM
    
    console.log(`Using specific operational date: ${dateRange} (6:00 AM to 5:59 AM next day)`);
  } else {
    // It's a predefined range - operational day logic (6:00 AM to 5:59 AM)
    switch(dateRange) {
      case 'today':
        // Current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        break;
      case 'yesterday':
        // Previous operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - day before yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 2);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() - 1);
          endDate.setHours(5, 59, 59, 999);
        }
        break;
      case 'week':
        // Last 7 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(6, 0, 0, 0);
        break;
      case 'month':
        // Last 30 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 30);
        startDate.setHours(6, 0, 0, 0);
        break;
      case 'year':
        // Last 365 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 365);
        startDate.setHours(6, 0, 0, 0);
        break;
      default:
        // Default to current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
    }
  }
  
  // Convert to IST for logging (UTC + 5:30)
  const startIST = new Date(startDate.getTime() + (5.5 * 60 * 60 * 1000));
  const endIST = new Date(endDate.getTime() + (5.5 * 60 * 60 * 1000));
  
  console.log(`📅 Enhanced Date Range (UTC): ${startDate.toISOString()} to ${endDate.toISOString()}`);
  console.log(`🇮🇳 Enhanced Date Range (IST): ${startIST.toLocaleString('en-IN')} to ${endIST.toLocaleString('en-IN')}`);
  
  return { startDate, endDate };
}

/**
 * Generate cache key for dashboard data
 */
function generateCacheKey(endpoint, userId, role, filters) {
  const filterString = JSON.stringify(filters);
  return `dashboard:${endpoint}:${role}:${userId}:${filterString}`;
}

/**
 * Check if data should be cached based on date range
 */
function shouldCache(dateRange) {
  // Don't cache real-time data (today) for too long
  if (dateRange === 'today') return false;
  
  // Cache historical data longer
  return true;
}

/**
 * Get cache TTL based on date range
 */
function getCacheTTL(dateRange) {
  switch(dateRange) {
    case 'today':
      return 60; // 1 minute for today's data
    case 'yesterday':
      return 300; // 5 minutes for yesterday
    case 'week':
      return 600; // 10 minutes for week
    case 'month':
      return 1800; // 30 minutes for month
    case 'year':
      return 3600; // 1 hour for year
    default:
      return 300; // 5 minutes default
  }
}

const enhancedDashboardController = {
  /**
   * ===============================================================================
   * ## ENHANCED DASHBOARD SUMMARY WITH REDIS CACHING
   * ===============================================================================
   */
  getDashboardSummary: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId, laneId } = req.query;
      const { id: userId, role } = req.user;
      
      // Create filters object
      const filters = { dateRange, companyId, plazaId, laneId };
      
      // Check cache first
      const cachedData = await redisService.getDashboardSummary(userId, role, filters);
      if (cachedData) {
        console.log('✅ Dashboard summary served from cache');
        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          timestamp: new Date().toISOString()
        });
      }

      console.log('❌ Cache miss - fetching dashboard summary from database');

      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Optimize date range for performance
      let optimizedStartDate = new Date(startDate);
      let optimizedEndDate = new Date(endDate);
      
      const maxRangeDays = 90;
      const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      if (requestedRangeDays > maxRangeDays) {
        optimizedStartDate = new Date(optimizedEndDate);
        optimizedStartDate.setDate(optimizedEndDate.getDate() - maxRangeDays);
      }
      
      // Base query parameters
      const queryParams = { 
        startDate: optimizedStartDate, 
        endDate: optimizedEndDate
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      let laneFilter = '';
      if (laneId) {
        laneFilter = 'AND (t.EntryLane = @laneId OR t.ExitLane = @laneId)';
        queryParams.laneId = laneId;
      }
      
      // Main summary query with performance optimizations
      const summaryQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
          COUNT(*) as TransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration,
          COUNT(DISTINCT t.PlazaCode) as ActivePlazas,
          MAX(t.ExitDateTime) as LastTransaction
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      const summaryResult = await db.query(summaryQuery, queryParams);
      
      // Get trend data (compare to previous period)
      const prevStartDate = new Date(optimizedStartDate);
      const prevEndDate = new Date(optimizedEndDate);
      const timeDiff = prevEndDate - prevStartDate;
      
      prevStartDate.setTime(prevStartDate.getTime() - timeDiff);
      prevEndDate.setTime(prevEndDate.getTime() - timeDiff);
      
      const trendQueryParams = { 
        ...queryParams, 
        prevStartDate, 
        prevEndDate 
      };
      
      const trendQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as PrevRevenue,
          COUNT(*) as PrevTransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as PrevVehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as PrevAvgDuration
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @prevStartDate AND @prevEndDate
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN)
      `;
      
      const trendResult = await db.query(trendQuery, trendQueryParams);
      
      // Process results
      const summary = summaryResult.recordset[0];
      const trend = trendResult.recordset[0];
      
      // Calculate percentage changes
      const revenueChange = trend.PrevRevenue > 0 
        ? ((summary.TotalRevenue - trend.PrevRevenue) / trend.PrevRevenue * 100).toFixed(2)
        : summary.TotalRevenue > 0 ? 100 : 0;
        
      const transactionChange = trend.PrevTransactionCount > 0 
        ? ((summary.TransactionCount - trend.PrevTransactionCount) / trend.PrevTransactionCount * 100).toFixed(2)
        : summary.TransactionCount > 0 ? 100 : 0;
        
      const vehicleChange = trend.PrevVehicleCount > 0 
        ? ((summary.VehicleCount - trend.PrevVehicleCount) / trend.PrevVehicleCount * 100).toFixed(2)
        : summary.VehicleCount > 0 ? 100 : 0;
        
      const durationChange = trend.PrevAvgDuration > 0 
        ? ((summary.AvgDuration - trend.PrevAvgDuration) / trend.PrevAvgDuration * 100).toFixed(2)
        : summary.AvgDuration > 0 ? 100 : 0;

      // Prepare response data
      const responseData = {
        summary: {
          totalRevenue: parseFloat(summary.TotalRevenue) || 0,
          transactionCount: parseInt(summary.TransactionCount) || 0,
          vehicleCount: parseInt(summary.VehicleCount) || 0,
          avgDuration: parseFloat(summary.AvgDuration) || 0,
          activePlazas: parseInt(summary.ActivePlazas) || 0,
          lastTransaction: summary.LastTransaction
        },
        trends: {
          revenueChange: parseFloat(revenueChange),
          transactionChange: parseFloat(transactionChange),
          vehicleChange: parseFloat(vehicleChange),
          durationChange: parseFloat(durationChange)
        },
        period: {
          startDate: optimizedStartDate,
          endDate: optimizedEndDate,
          dateRange
        },
        metadata: {
          queryTime: new Date().toISOString(),
          cached: false,
          role,
          filters
        }
      };

      // Cache the results
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheDashboardSummary(userId, role, filters, responseData);
      
      console.log(`✅ Dashboard summary cached for ${cacheTTL} seconds`);

      res.json({
        success: true,
        data: responseData,
        cached: false,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Enhanced dashboard summary error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard summary',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  },

  /**
   * ===============================================================================
   * ## ENHANCED REVENUE BY PAYMENT METHOD WITH CACHING
   * ===============================================================================
   */
  getRevenueByPaymentMethod: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      const filters = { dateRange, companyId, plazaId };
      
      // Check cache first
      const cachedData = await redisService.getRevenueByPayment(userId, role, filters);
      if (cachedData) {
        console.log('✅ Revenue by payment method served from cache');
        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          timestamp: new Date().toISOString()
        });
      }

      console.log('❌ Cache miss - fetching revenue by payment method from database');

      const { startDate, endDate } = calculateDateRange(dateRange);
      const queryParams = { startDate, endDate };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }

      const revenueQuery = `
        SELECT
          CASE 
            WHEN t.PaymentMode = 'Cash' THEN 'Cash'
            WHEN t.PaymentMode = 'Card' THEN 'Card'
            WHEN t.PaymentMode = 'UPI' THEN 'UPI'
            WHEN t.PaymentMode = 'Fastag' THEN 'FASTag'
            WHEN t.PaymentMode = 'Digital' THEN 'Digital'
            ELSE 'Other'
          END as PaymentMethod,
          SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as Revenue,
          COUNT(*) as TransactionCount,
          AVG(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as AvgAmount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY 
          CASE 
            WHEN t.PaymentMode = 'Cash' THEN 'Cash'
            WHEN t.PaymentMode = 'Card' THEN 'Card'
            WHEN t.PaymentMode = 'UPI' THEN 'UPI'
            WHEN t.PaymentMode = 'Fastag' THEN 'FASTag'
            WHEN t.PaymentMode = 'Digital' THEN 'Digital'
            ELSE 'Other'
          END
        ORDER BY Revenue DESC
        OPTION (OPTIMIZE FOR UNKNOWN)
      `;

      const result = await db.query(revenueQuery, queryParams);
      
      const responseData = {
        paymentMethods: result.recordset.map(row => ({
          method: row.PaymentMethod,
          revenue: parseFloat(row.Revenue) || 0,
          transactionCount: parseInt(row.TransactionCount) || 0,
          avgAmount: parseFloat(row.AvgAmount) || 0,
          percentage: 0 // Will be calculated below
        })),
        period: {
          startDate,
          endDate,
          dateRange
        },
        metadata: {
          queryTime: new Date().toISOString(),
          cached: false,
          role,
          filters
        }
      };

      // Calculate percentages
      const totalRevenue = responseData.paymentMethods.reduce((sum, method) => sum + method.revenue, 0);
      responseData.paymentMethods.forEach(method => {
        method.percentage = totalRevenue > 0 ? ((method.revenue / totalRevenue) * 100).toFixed(2) : 0;
      });

      // Cache the results
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheRevenueByPayment(userId, role, filters, responseData);
      
      console.log(`✅ Revenue by payment method cached for ${cacheTTL} seconds`);

      res.json({
        success: true,
        data: responseData,
        cached: false,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Enhanced revenue by payment method error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch revenue by payment method',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  },

  /**
   * ===============================================================================
   * ## REAL-TIME DASHBOARD DATA (NO CACHE)
   * ===============================================================================
   */
  getRealTimeDashboard: async (req, res) => {
    try {
      const { plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // This endpoint always returns fresh data
      const now = new Date();
      const startOfDay = new Date(now);
      startOfDay.setHours(0, 0, 0, 0);
      
      const queryParams = { 
        startDate: startOfDay, 
        endDate: now 
      };
      
      // Apply role-based filtering
      let plazaFilter = '';
      
      if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }

      const realTimeQuery = `
        SELECT
          COUNT(*) as TodayTransactions,
          SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as TodayRevenue,
          COUNT(CASE WHEN t.ExitDateTime >= DATEADD(HOUR, -1, GETDATE()) THEN 1 END) as LastHourTransactions,
          COUNT(CASE WHEN t.ExitDateTime >= DATEADD(MINUTE, -15, GETDATE()) THEN 1 END) as Last15MinTransactions,
          COUNT(DISTINCT t.PlazaCode) as ActivePlazas,
          MAX(t.ExitDateTime) as LastTransaction
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${plazaFilter}
        OPTION (OPTIMIZE FOR UNKNOWN)
      `;

      const result = await db.query(realTimeQuery, queryParams);
      const data = result.recordset[0];

      const responseData = {
        realTime: {
          todayTransactions: parseInt(data.TodayTransactions) || 0,
          todayRevenue: parseFloat(data.TodayRevenue) || 0,
          lastHourTransactions: parseInt(data.LastHourTransactions) || 0,
          last15MinTransactions: parseInt(data.Last15MinTransactions) || 0,
          activePlazas: parseInt(data.ActivePlazas) || 0,
          lastTransaction: data.LastTransaction,
          timestamp: new Date().toISOString()
        },
        metadata: {
          queryTime: new Date().toISOString(),
          cached: false,
          realTime: true,
          role
        }
      };

      res.json({
        success: true,
        data: responseData,
        cached: false,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Real-time dashboard error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch real-time dashboard data',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  },

  /**
   * ===============================================================================
   * ## CACHE MANAGEMENT ENDPOINTS
   * ===============================================================================
   */
  clearDashboardCache: async (req, res) => {
    try {
      const { id: userId, role } = req.user;
      
      // Only allow SuperAdmin to clear all cache
      if (role === 'SuperAdmin' && req.query.all === 'true') {
        await redisService.invalidateAllDashboardCache();
        console.log('✅ All dashboard cache cleared by SuperAdmin');
        
        return res.json({
          success: true,
          message: 'All dashboard cache cleared successfully'
        });
      }
      
      // Clear user-specific cache
      await redisService.invalidateDashboardCache(userId, role);
      console.log(`✅ Dashboard cache cleared for user ${userId} with role ${role}`);
      
      res.json({
        success: true,
        message: 'Dashboard cache cleared successfully'
      });

    } catch (error) {
      console.error('Clear dashboard cache error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear dashboard cache',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  },

  /**
   * ===============================================================================
   * ## CACHE STATUS ENDPOINT
   * ===============================================================================
   */
  getCacheStatus: async (req, res) => {
    try {
      const redisInfo = await redisService.getRedisInfo();
      
      res.json({
        success: true,
        data: {
          redis: redisInfo,
          cacheKeys: {
            dashboard: 'dashboard:*',
            user: 'session:user:*',
            permissions: 'permissions:user:*'
          },
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Cache status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get cache status',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
};

module.exports = enhancedDashboardController;