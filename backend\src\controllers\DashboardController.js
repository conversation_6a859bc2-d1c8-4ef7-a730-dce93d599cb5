const db = require('../config/database');
const sql = require('mssql');
const redisService = require('../services/RedisService');

/**
 * ===============================================================================
 * # Dashboard Controller
 * ===============================================================================
 *
 * This controller provides endpoints for dashboard metrics and visualizations
 * with role-based access control. It aggregates data from the parking system
 * to provide insights based on user roles (SuperAdmin, CompanyAdmin, PlazaManager).
 *
 * @module DashboardController
 */

/**
 * Get cache TTL based on date range
 */
function getCacheTTL(dateRange) {
  switch(dateRange) {
    case 'today':
      return 60; // 1 minute for today's data
    case 'yesterday':
      return 300; // 5 minutes for yesterday
    case 'week':
      return 600; // 10 minutes for week
    case 'month':
      return 1800; // 30 minutes for month
    case 'year':
      return 3600; // 1 hour for year
    default:
      return 300; // 5 minutes default
  }
}

/**
 * Helper function to calculate date range based on selection
 * @param {string} dateRange - The date range selection (today, yesterday, week, month, year, or specific date in YYYY-MM-DD format)
 * @returns {Object} Object with startDate and endDate
 */
function calculateDateRange(dateRange) {
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date - operational day from 6:00 AM to 5:59 AM next day
    startDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate.setDate(endDate.getDate() + 1); // Next day
    endDate.setSeconds(endDate.getSeconds() - 1); // 5:59:59 AM
    
    console.log(`Using specific operational date: ${dateRange} (6:00 AM to 5:59 AM next day)`);
  } else {
    // It's a predefined range - operational day logic (6:00 AM to 5:59 AM)
    switch(dateRange) {
      case 'today':
        // Current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        break;
      case 'yesterday':
        // Previous operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - day before yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 2);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() - 1);
          endDate.setHours(5, 59, 59, 999);
        }
        break;
      case 'week':
        // Last 7 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(6, 0, 0, 0);
        break;
      case 'month':
        // Last 30 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 30);
        startDate.setHours(6, 0, 0, 0);
        break;
      case 'year':
        // Last 365 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 365);
        startDate.setHours(6, 0, 0, 0);
        break;
      default:
        // Default to current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
    }
  }
  
  // Convert to IST for logging (UTC + 5:30)
  const startIST = new Date(startDate.getTime() + (5.5 * 60 * 60 * 1000));
  const endIST = new Date(endDate.getTime() + (5.5 * 60 * 60 * 1000));
  
  console.log(`📅 Date Range (UTC): ${startDate.toISOString()} to ${endDate.toISOString()}`);
  console.log(`🇮🇳 Date Range (IST): ${startIST.toLocaleString('en-IN')} to ${endIST.toLocaleString('en-IN')}`);
  
  return { startDate, endDate };
}
const dashboardController = {
  /**
   * ===============================================================================
   * ## GET DASHBOARD SUMMARY
   * ===============================================================================
   *
   * Returns summary metrics for the dashboard based on user role and filters.
   * Different metrics are provided based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with dashboard metrics
   */
  getDashboardSummary: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId, laneId } = req.query;
      const { id: userId, role } = req.user;
      
      // Create filters object for caching
      const filters = { dateRange, companyId, plazaId, laneId };
      
      // Check Redis cache first
      const cachedData = await redisService.getDashboardSummary(userId, role, filters);
      if (cachedData) {
        console.log('✅ Dashboard summary served from Redis cache');
        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          timestamp: new Date().toISOString()
        });
      }
      
      console.log('❌ Cache miss - fetching dashboard summary from database');
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Limit date range for large queries to improve performance
      let optimizedStartDate = new Date(startDate);
      let optimizedEndDate = new Date(endDate);
      
      // If date range is more than 90 days, limit to 90 days for better performance
      const maxRangeDays = 90; // 3 months max
      const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      if (requestedRangeDays > maxRangeDays) {
        optimizedStartDate = new Date(optimizedEndDate);
        optimizedStartDate.setDate(optimizedEndDate.getDate() - maxRangeDays);
      }
      
      // Base query parameters
      const queryParams = { 
        startDate: optimizedStartDate, 
        endDate: optimizedEndDate
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      let laneFilter = '';
      if (laneId) {
        laneFilter = 'AND (t.EntryLane = @laneId OR t.ExitLane = @laneId)';
        queryParams.laneId = laneId;
      }
      
      // Optimize query with indexes and performance hints
      const summaryQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
          COUNT(*) as TransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      const summaryResult = await db.query(summaryQuery, queryParams);
      
      // Get trend data (compare to previous period)
      const prevStartDate = new Date(optimizedStartDate);
      const prevEndDate = new Date(optimizedEndDate);
      const timeDiff = prevEndDate - prevStartDate;
      
      prevStartDate.setTime(prevStartDate.getTime() - timeDiff);
      prevEndDate.setTime(prevEndDate.getTime() - timeDiff);
      
      const trendQueryParams = { 
        ...queryParams, 
        prevStartDate, 
        prevEndDate 
      };
      
      const trendQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as PrevRevenue,
          COUNT(*) as PrevTransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as PrevVehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as PrevAvgDuration
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @prevStartDate AND @prevEndDate
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN)
      `;
      
      const trendResult = await db.query(trendQuery, trendQueryParams);
      
      // Calculate trends
      const summary = summaryResult.recordset[0];
      const prevSummary = trendResult.recordset[0];
      
      const calculateTrend = (current, previous) => {
        if (!previous || previous === 0) return 0;
        return Math.round(((current - previous) / previous) * 100);
      };
      
      const response = {
        totalRevenue: summary.TotalRevenue || 0,
        transactionCount: summary.TransactionCount || 0,
        vehicleCount: summary.VehicleCount || 0,
        avgDuration: summary.AvgDuration || 0,
        revenueTrend: calculateTrend(summary.TotalRevenue, prevSummary.PrevRevenue),
        transactionTrend: calculateTrend(summary.TransactionCount, prevSummary.PrevTransactionCount),
        vehicleTrend: calculateTrend(summary.VehicleCount, prevSummary.PrevVehicleCount),
        durationTrend: calculateTrend(summary.AvgDuration, prevSummary.PrevAvgDuration),
        // Add metadata about query optimization
        meta: {
          dateRangeOptimized: requestedRangeDays > maxRangeDays,
          originalDateRange: {
            start: startDate,
            end: endDate
          },
          queryDateRange: {
            start: optimizedStartDate,
            end: optimizedEndDate
          }
        }
      };
      
      // Cache the results with appropriate TTL based on date range
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheDashboardSummary(userId, role, filters, response);
      console.log(`✅ Dashboard summary cached for ${cacheTTL} seconds`);
      
      return res.status(200).json({
        success: true,
        data: response,
        cached: false,
        message: 'Dashboard summary retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getDashboardSummary:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve dashboard summary',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PAYMENT METHOD
   * ===============================================================================
   *
   * Returns revenue breakdown by payment method with transaction counts.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with payment method revenue data
   */
  getRevenueByPaymentMethod: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Create filters object for caching
      const filters = { dateRange, companyId, plazaId };
      
      // Check Redis cache first
      const cachedData = await redisService.getRevenueByPayment(userId, role, filters);
      if (cachedData) {
        console.log('✅ Revenue by payment method served from Redis cache');
        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          message: 'Revenue by payment method retrieved successfully'
        });
      }
      
      console.log('❌ Cache miss - fetching revenue by payment method from database');
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute payment method query
      const paymentMethodQuery = `
        SELECT
          ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY t.PaymentMode
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(paymentMethodQuery, queryParams);
      
      // Cache the results
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheRevenueByPayment(userId, role, filters, result.recordset);
      console.log(`✅ Revenue by payment method cached for ${cacheTTL} seconds`);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        cached: false,
        message: 'Revenue by payment method retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPaymentMethod:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by payment method',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET RECENT TRANSACTIONS
   * ===============================================================================
   *
   * Returns the most recent transactions based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with recent transactions
   */
  getRecentTransactions: async (req, res) => {
    try {
      const { limit = 5, companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Base query parameters
      const queryParams = { 
        limit: parseInt(limit) 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute recent transactions query
      const recentTransactionsQuery = `
        SELECT TOP(@limit)
          t.PakringDataID,
          t.PlazaName,
          t.VehicleNumber,
          t.EntryDateTime,
          t.ExitDateTime,
          t.EntryLane,
          t.ExitLane,
          t.ParkedDuration,
          t.ParkingFee,
          t.iTotalGSTFee,
          t.PaymentMode,
          t.PaymentType
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime IS NOT NULL
        ${companyFilter}
        ${plazaFilter}
        ORDER BY t.ExitDateTime DESC
      `;
      
      const result = await db.query(recentTransactionsQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Recent transactions retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRecentTransactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve recent transactions',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET PEAK HOURS DATA
   * ===============================================================================
   *
   * Returns transaction counts by hour of day to identify peak hours.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with hourly transaction data
   */
  getPeakHoursData: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute peak hours query
      const peakHoursQuery = `
        SELECT
          DATEPART(HOUR, t.ExitDateTime) as hour,
          COUNT(*) as count
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY DATEPART(HOUR, t.ExitDateTime)
        ORDER BY hour
      `;
      
      const result = await db.query(peakHoursQuery, queryParams);
      
      // Fill in missing hours with zero counts
      const hourlyData = Array(24).fill().map((_, i) => ({
        hour: i,
        count: 0
      }));
      
      result.recordset.forEach(row => {
        hourlyData[row.hour].count = row.count;
      });
      
      return res.status(200).json({
        success: true,
        data: hourlyData,
        message: 'Peak hours data retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getPeakHoursData:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve peak hours data',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PLAZA
   * ===============================================================================
   *
   * Returns revenue breakdown by plaza with transaction counts.
   * Only accessible to SuperAdmin and CompanyAdmin roles.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with plaza revenue data
   */
  getRevenueByPlaza: async (req, res) => {
    try {
      const { dateRange = 'today', companyId } = req.query;
      const { id: userId, role } = req.user;
      
      // Only SuperAdmin and CompanyAdmin can access this endpoint
      if (role !== 'SuperAdmin' && role !== 'CompanyAdmin') {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this resource'
        });
      }
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      // Execute plaza revenue query
      const plazaRevenueQuery = `
        SELECT
          t.PlazaName,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        GROUP BY t.PlazaName
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(plazaRevenueQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Revenue by plaza retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPlaza:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by plaza',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANE STATUS
   * ===============================================================================
   *
   * Returns the current status of lanes for a specific plaza.
   * Primarily used by PlazaManager role.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lane status data
   */
  getLaneStatus: async (req, res) => {
    try {
      const { plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      if (!plazaId) {
        return res.status(400).json({
          success: false,
          message: 'Plaza ID is required'
        });
      }
      
      // Check if user has access to this plaza
      if (role !== 'SuperAdmin') {
        let accessQuery;
        
        if (role === 'CompanyAdmin') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
          `;
        } else if (role === 'PlazaManager') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM UserPlaza
            WHERE PlazaId = @plazaId AND UserId = @userId AND IsActive = 1
          `;
        }
        
        const accessResult = await db.query(accessQuery, {
          plazaId,
          userId
        });
        
        if (accessResult.recordset[0].count === 0) {
          return res.status(403).json({
            success: false,
            message: 'You do not have access to this plaza'
          });
        }
      }
      
      // Get lane status
      const laneStatusQuery = `
        SELECT
          l.LaneID,
          l.LaneNumber,
          l.LaneType,
          l.ActiveStatus,
          CASE 
            WHEN l.ActiveStatus = 1 THEN 'Active'
            ELSE 'Inactive'
          END as Status,
          (
            SELECT COUNT(*)
            FROM tblParkwiz_Parking_Data t
            WHERE (t.EntryLane = l.LaneNumber OR t.ExitLane = l.LaneNumber)
            AND t.PlazaCode = p.PlazaCode
            AND t.ExitDateTime >= DATEADD(HOUR, -24, GETDATE())
          ) as TransactionsLast24Hours
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.PlazaID = @plazaId
        ORDER BY l.LaneNumber
      `;
      
      const result = await db.query(laneStatusQuery, { plazaId });
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Lane status retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getLaneStatus:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve lane status',
        error: error.message
      });
    }
  }
};

module.exports = dashboardController;