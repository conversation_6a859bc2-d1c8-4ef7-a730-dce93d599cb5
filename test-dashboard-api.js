require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

// Import the dashboard controller logic
async function testDashboardAPI() {
  try {
    console.log('=== DASHBOARD API ENDPOINTS TEST ===');
    console.log('Current Time:', new Date().toISOString());
    console.log();

    // Connect to database
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('✅ Connected to database successfully!');

    // Test 1: Dashboard Summary (like the API endpoint)
    console.log('\n🔍 TESTING DASHBOARD SUMMARY API...');
    
    const dateRange = 'today';
    let startDate, endDate;
    const referenceDate = new Date();
    
    // Calculate date range (same logic as DashboardController)
    switch(dateRange) {
      case 'today':
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setDate(endDate.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 6);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 29);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
    }

    console.log('Date Range:', startDate.toISOString(), 'to', endDate.toISOString());

    // Main summary query (same as DashboardController)
    const summaryQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
        COUNT(*) as transactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as vehicleCount,
        AVG(ISNULL(t.ParkedDuration, 0)) as avgDuration
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
    `;

    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);
    
    const summaryResult = await request.query(summaryQuery);
    const summary = summaryResult.recordset[0];

    console.log('Summary Results:');
    console.log('  Total Revenue: ₹' + summary.totalRevenue.toLocaleString());
    console.log('  Transaction Count:', summary.transactionCount.toLocaleString());
    console.log('  Vehicle Count:', summary.vehicleCount.toLocaleString());
    console.log('  Avg Duration:', Math.round(summary.avgDuration || 0), 'minutes');

    // Test 2: Revenue by Payment Method
    console.log('\n💳 TESTING REVENUE BY PAYMENT METHOD API...');
    
    const paymentMethodQuery = `
      SELECT 
        t.PaymentMode as paymentMode,
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
        COUNT(*) as transactionCount
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY t.PaymentMode
      ORDER BY totalRevenue DESC
    `;

    const paymentResult = await request.query(paymentMethodQuery);
    
    console.log('Payment Method Results:');
    paymentResult.recordset.forEach(payment => {
      console.log(`  ${payment.paymentMode || 'Unknown'}: ${payment.transactionCount.toLocaleString()} transactions, ₹${payment.totalRevenue.toLocaleString()}`);
    });

    // Test 3: Peak Hours Data
    console.log('\n⏰ TESTING PEAK HOURS API...');
    
    const peakHoursQuery = `
      SELECT 
        DATEPART(HOUR, t.ExitDateTime) as hour,
        COUNT(*) as count
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY DATEPART(HOUR, t.ExitDateTime)
      ORDER BY hour
    `;

    const peakHoursResult = await request.query(peakHoursQuery);
    
    console.log('Peak Hours Results:');
    peakHoursResult.recordset.forEach(hour => {
      console.log(`  ${hour.hour}:00 - ${hour.count.toLocaleString()} transactions`);
    });

    // Test 4: Recent Transactions
    console.log('\n🕐 TESTING RECENT TRANSACTIONS API...');
    
    const recentTransactionsQuery = `
      SELECT TOP 5
        t.VehicleNumber,
        t.PlazaName,
        t.ExitDateTime,
        t.PaymentMode,
        t.ExitLane,
        ISNULL(t.ParkingFee, 0) as ParkingFee,
        ISNULL(t.iTotalGSTFee, 0) as iTotalGSTFee
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      ORDER BY t.ExitDateTime DESC
    `;

    const recentResult = await request.query(recentTransactionsQuery);
    
    console.log('Recent Transactions Results:');
    recentResult.recordset.forEach(transaction => {
      const totalAmount = (transaction.ParkingFee || 0) + (transaction.iTotalGSTFee || 0);
      console.log(`  ${transaction.VehicleNumber || 'Unknown'} at ${transaction.PlazaName} - ₹${totalAmount.toFixed(2)} (${transaction.PaymentMode}) - ${transaction.ExitDateTime.toISOString()}`);
    });

    // Test 5: Check if data matches different date ranges
    console.log('\n📊 TESTING DIFFERENT DATE RANGES...');
    
    const dateRanges = ['today', 'yesterday', 'week', 'month'];
    
    for (const range of dateRanges) {
      let rangeStart, rangeEnd;
      const ref = new Date();
      
      switch(range) {
        case 'today':
          rangeStart = new Date(ref);
          rangeStart.setHours(0, 0, 0, 0);
          rangeEnd = new Date(ref);
          rangeEnd.setHours(23, 59, 59, 999);
          break;
        case 'yesterday':
          rangeStart = new Date(ref);
          rangeStart.setDate(rangeStart.getDate() - 1);
          rangeStart.setHours(0, 0, 0, 0);
          rangeEnd = new Date(ref);
          rangeEnd.setDate(rangeEnd.getDate() - 1);
          rangeEnd.setHours(23, 59, 59, 999);
          break;
        case 'week':
          rangeStart = new Date(ref);
          rangeStart.setDate(rangeStart.getDate() - 6);
          rangeStart.setHours(0, 0, 0, 0);
          rangeEnd = new Date(ref);
          rangeEnd.setHours(23, 59, 59, 999);
          break;
        case 'month':
          rangeStart = new Date(ref);
          rangeStart.setDate(rangeStart.getDate() - 29);
          rangeStart.setHours(0, 0, 0, 0);
          rangeEnd = new Date(ref);
          rangeEnd.setHours(23, 59, 59, 999);
          break;
      }

      const rangeRequest = new sql.Request();
      rangeRequest.input('startDate', sql.DateTime, rangeStart);
      rangeRequest.input('endDate', sql.DateTime, rangeEnd);
      
      const rangeResult = await rangeRequest.query(summaryQuery);
      const rangeData = rangeResult.recordset[0];
      
      console.log(`  ${range.toUpperCase()}: ${rangeData.transactionCount.toLocaleString()} transactions, ₹${rangeData.totalRevenue.toLocaleString()}`);
    }

    await sql.close();
    
    console.log('\n✅ Dashboard API endpoints test completed successfully!');
    console.log('\n📋 SUMMARY:');
    console.log('  ✅ Database connection working');
    console.log('  ✅ Summary data fetching correctly');
    console.log('  ✅ Payment method breakdown working');
    console.log('  ✅ Peak hours data available');
    console.log('  ✅ Recent transactions loading');
    console.log('  ✅ All date ranges functional');
    
  } catch (error) {
    console.error('❌ Error testing dashboard API:', error);
    if (sql.connected) {
      await sql.close();
    }
  }
}

testDashboardAPI();