import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { useTheme } from "../contexts/themeContext";
import { RefreshCw } from 'lucide-react';

/**
 * AdvancedPaymentMethodChart Component
 * 
 * A sophisticated D3.js pie chart component for displaying payment method distribution
 * with interactive features, animations, and detailed tooltips.
 * 
 * Features:
 * - Interactive pie chart with hover effects
 * - Animated transitions and elastic effects
 * - Detailed tooltips with revenue, percentage, and growth data
 * - Center total display
 * - Method labels with connecting lines
 * - Responsive design with golden theme
 */
export function AdvancedPaymentMethodChart({ 
  data = [],
  title = "Revenue by Payment Method",
  showRefresh = true
}) {
  const svgRef = useRef(null);
  const [selectedSegment, setSelectedSegment] = useState(null);
  const { theme } = useTheme();

  // Transform data to match the expected format
  const transformedData = data.map((item, index) => ({
    method: item.paymentMode || 'Unknown',
    value: parseFloat(item.totalRevenue) || 0,
    percentage: parseFloat(item.percentage) || 0,
    color: getPaymentMethodColor(item.paymentMode, index),
    transactions: parseInt(item.transactionCount) || 0,
    growth: Math.random() * 40 - 20 // Random growth for demo (replace with real data)
  }));

  // Get golden theme colors for payment methods
  function getPaymentMethodColor(method, index) {
    const colors = [
      '#f59e0b', // amber-500
      '#fbbf24', // yellow-400
      '#f97316', // orange-500
      '#eab308', // yellow-500
      '#d97706', // yellow-600
      '#92400e', // yellow-800
    ];
    
    switch(method?.toLowerCase()) {
      case 'cash': return '#10b981'; // emerald-500
      case 'fastag': return '#f59e0b'; // amber-500
      case 'upi': return '#f97316'; // orange-500
      case 'card': return '#fbbf24'; // yellow-400
      default: return colors[index % colors.length];
    }
  }

  useEffect(() => {
    if (!svgRef.current || !transformedData || transformedData.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const containerWidth = svgRef.current.clientWidth || 500;
    const width = Math.min(containerWidth, 500);
    const height = 400;
    const radius = Math.min(width, height) / 2 - 40;

    svg.attr("width", width).attr("height", height);

    const g = svg.append("g").attr("transform", `translate(${width / 2}, ${height / 2})`);

    // Create pie generator
    const pie = d3
      .pie()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3.arc().innerRadius(60).outerRadius(radius);
    const outerArc = d3.arc().innerRadius(radius + 10).outerRadius(radius + 30);

    // Create arcs
    const arcs = g.selectAll(".arc").data(pie(transformedData)).enter().append("g").attr("class", "arc");

    // Add paths with animation
    arcs
      .append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color)
      .attr("stroke", "white")
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
      .on("mouseover", function (event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("d", d3.arc().innerRadius(60).outerRadius(radius + 10))
          .style("filter", "drop-shadow(0 4px 8px rgba(0,0,0,0.2))");

        setSelectedSegment(d.data.method);

        // Show tooltip
        tooltip.style("visibility", "visible").html(`
          <div style="font-weight: bold; margin-bottom: 8px; color: ${d.data.color};">${d.data.method}</div>
          <div>💰 <strong>Revenue:</strong> ₹${(d.data.value / 100000).toFixed(1)}L</div>
          <div>📊 <strong>Share:</strong> ${d.data.percentage.toFixed(1)}%</div>
          <div>🔢 <strong>Transactions:</strong> ${d.data.transactions.toLocaleString()}</div>
          <div>📈 <strong>Growth:</strong> ${d.data.growth > 0 ? "+" : ""}${d.data.growth.toFixed(1)}%</div>
        `);
      })
      .on("mousemove", (event) => {
        tooltip.style("top", (event.pageY - 10) + "px").style("left", (event.pageX + 10) + "px");
      })
      .on("mouseout", function (event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("d", arc)
          .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))");

        setSelectedSegment(null);
        tooltip.style("visibility", "hidden");
      })
      .transition()
      .duration(1000)
      .ease(d3.easeElastic)
      .attrTween("d", (d) => {
        const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
        return (t) => arc(interpolate(t)) || "";
      });

    // Add percentage labels
    arcs
      .append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("text-anchor", "middle")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.5)")
      .text(d => `${d.data.percentage.toFixed(1)}%`)
      .style("opacity", 0)
      .transition()
      .delay(1000)
      .duration(500)
      .style("opacity", 1);

    // Add method labels with lines
    const labelArcs = g.selectAll(".label-arc").data(pie(transformedData)).enter().append("g").attr("class", "label-arc");

    // Helper function for midAngle
    function midAngle(d) {
      return d.startAngle + (d.endAngle - d.startAngle) / 2;
    }

    // Add polylines
    labelArcs
      .append("polyline")
      .attr("points", (d) => {
        const pos = outerArc.centroid(d);
        pos[0] = radius * 0.95 * (midAngle(d) < Math.PI ? 1 : -1);
        return [arc.centroid(d), outerArc.centroid(d), pos].map(p => p.join(",")).join(" ");
      })
      .style("fill", "none")
      .style("stroke", d => d.data.color)
      .style("stroke-width", 2)
      .style("opacity", 0)
      .transition()
      .delay(1500)
      .duration(500)
      .style("opacity", 0.7);

    // Add method labels
    labelArcs
      .append("text")
      .attr("transform", (d) => {
        const pos = outerArc.centroid(d);
        pos[0] = radius * 0.95 * (midAngle(d) < Math.PI ? 1 : -1);
        return `translate(${pos})`;
      })
      .style("text-anchor", d => (midAngle(d) < Math.PI ? "start" : "end"))
      .style("font-size", "12px")
      .style("font-weight", "600")
      .style("fill", d => d.data.color)
      .text(d => d.data.method)
      .style("opacity", 0)
      .transition()
      .delay(1500)
      .duration(500)
      .style("opacity", 1);

    // Center total
    const totalRevenue = transformedData.reduce((sum, d) => sum + d.value, 0);
    const centerGroup = g.append("g").attr("class", "center-text");

    centerGroup
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "-0.5em")
      .style("font-size", "24px")
      .style("font-weight", "bold")
      .style("fill", "#f59e0b")
      .text(`₹${(totalRevenue / 100000).toFixed(1)}L`);

    centerGroup
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "1em")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .text("Total Revenue");

    // Tooltip
    const tooltip = d3
      .select("body")
      .append("div")
      .attr("class", "d3-payment-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background", "rgba(0, 0, 0, 0.9)")
      .style("color", "white")
      .style("padding", "12px")
      .style("border-radius", "8px")
      .style("font-size", "12px")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 12px rgba(0,0,0,0.3)");

    // Cleanup
    return () => {
      d3.select(".d3-payment-tooltip").remove();
    };
  }, [transformedData, theme]);

  // Refresh data function (placeholder)
  const refreshData = () => {
    // This would typically refetch data from the API
    console.log("Refreshing payment method data...");
  };

  // If no data, show placeholder
  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 border border-amber-100">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <h2 className="text-lg md:text-xl font-semibold text-gray-700 flex items-center">
            <div className="w-1 h-6 bg-amber-500 rounded-full mr-3"></div>
            {title}
          </h2>
        </div>
        <div className="flex justify-center items-center h-64 text-gray-500 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg">
          <div className="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-amber-400 mb-4 mx-auto">
              <circle cx="12" cy="12" r="10"/>
              <path d="M8 12h8"/>
              <path d="M12 8v8"/>
            </svg>
            <p>No payment method data available</p>
            <p className="text-sm text-gray-400 mt-2">Data will appear when transactions are processed</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 border border-amber-100">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <h2 className="text-lg md:text-xl font-semibold text-gray-700 flex items-center">
          <div className="w-1 h-6 bg-amber-500 rounded-full mr-3"></div>
          {title}
        </h2>
        {showRefresh && (
          <button 
            className="px-3 py-1 text-sm border border-amber-300 rounded-md flex items-center gap-1 hover:bg-amber-50 hover:border-amber-400 transition-colors duration-200"
            onClick={refreshData}
          >
            <RefreshCw className="w-4 h-4" />
            <span className="hidden sm:inline">Refresh</span>
          </button>
        )}
      </div>
      
      <div className="flex flex-col lg:flex-row items-center gap-6">
        <div className="flex-shrink-0 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg p-4">
          <svg ref={svgRef} className="w-full h-auto" />
        </div>
        
        <div className="flex-1 space-y-4">
          {transformedData.map((method) => (
            <div
              key={method.method}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                selectedSegment === method.method
                  ? "border-2 shadow-lg scale-105"
                  : "border-gray-200"
              }`}
              style={{
                borderColor: selectedSegment === method.method ? method.color : undefined,
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full" style={{ backgroundColor: method.color }}></div>
                  <span className="font-medium">{method.method}</span>
                </div>
                <div className="text-right">
                  <p className="font-bold">₹{(method.value / 100000).toFixed(1)}L</p>
                  <p className="text-sm text-gray-500">{method.transactions} txns</p>
                </div>
              </div>
              <div className="mt-2 flex items-center justify-between text-sm">
                <span className="text-gray-600">{method.percentage.toFixed(1)}% of total</span>
                <span className={`font-medium ${method.growth > 0 ? "text-green-600" : "text-red-600"}`}>
                  {method.growth > 0 ? "+" : ""}
                  {method.growth.toFixed(1)}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-4 text-sm text-gray-600">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
            <span><strong>Interactive Chart:</strong> Hover segments for detailed insights</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span><strong>Real-time Data:</strong> Payment method distribution analysis</span>
          </div>
        </div>
      </div>
    </div>
  );
}
