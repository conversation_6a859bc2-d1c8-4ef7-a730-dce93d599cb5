require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function testDashboardData() {
  try {
    console.log('=== DASHBOARD DATA FETCHING TEST ===');
    console.log('Current Time:', new Date().toISOString());
    console.log('Timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);
    console.log();

    // Connect to database
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('✅ Connected to database successfully!');

    // Calculate date ranges for today and yesterday (6 AM to 5:59 AM)
    const now = new Date();
    
    // Today's operational day: 6 AM today to 5:59 AM tomorrow
    const todayStart = new Date(now);
    todayStart.setHours(6, 0, 0, 0);
    const todayEnd = new Date(now);
    todayEnd.setDate(todayEnd.getDate() + 1);
    todayEnd.setHours(5, 59, 59, 999);
    
    // Yesterday's operational day: 6 AM yesterday to 5:59 AM today
    const yesterdayStart = new Date(now);
    yesterdayStart.setDate(yesterdayStart.getDate() - 1);
    yesterdayStart.setHours(6, 0, 0, 0);
    const yesterdayEnd = new Date(now);
    yesterdayEnd.setHours(5, 59, 59, 999);

    console.log('📅 DATE RANGES:');
    console.log('Today Operational Day:', todayStart.toISOString(), 'to', todayEnd.toISOString());
    console.log('Yesterday Operational Day:', yesterdayStart.toISOString(), 'to', yesterdayEnd.toISOString());
    console.log();

    // Test today's data
    console.log('🔍 TESTING TODAY\'S DATA...');
    const todayQuery = `
      SELECT
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(DISTINCT PlazaCode) as PlazaCount,
        COUNT(DISTINCT CASE WHEN VehicleNumber <> 'NA' AND VehicleNumber IS NOT NULL THEN VehicleNumber ELSE NULL END) as VehicleCount,
        AVG(ISNULL(ParkedDuration, 0)) as AvgDuration,
        MIN(ExitDateTime) as EarliestTransaction,
        MAX(ExitDateTime) as LatestTransaction
      FROM tblParkwiz_Parking_Data WITH (NOLOCK)
      WHERE ExitDateTime >= @startDate AND ExitDateTime <= @endDate
    `;

    const todayRequest = new sql.Request();
    todayRequest.input('startDate', sql.DateTime, todayStart);
    todayRequest.input('endDate', sql.DateTime, todayEnd);
    
    const todayResult = await todayRequest.query(todayQuery);
    const todayData = todayResult.recordset[0];

    console.log('Today\'s Results:');
    console.log('  Transactions:', todayData.TransactionCount.toLocaleString());
    console.log('  Revenue: ₹' + todayData.TotalRevenue.toLocaleString());
    console.log('  Plazas:', todayData.PlazaCount);
    console.log('  Vehicles:', todayData.VehicleCount.toLocaleString());
    console.log('  Avg Duration:', Math.round(todayData.AvgDuration || 0), 'minutes');
    console.log('  Date Range:', todayData.EarliestTransaction?.toISOString() || 'No data', 'to', todayData.LatestTransaction?.toISOString() || 'No data');
    console.log();

    // Test yesterday's data
    console.log('🔍 TESTING YESTERDAY\'S DATA...');
    const yesterdayRequest = new sql.Request();
    yesterdayRequest.input('startDate', sql.DateTime, yesterdayStart);
    yesterdayRequest.input('endDate', sql.DateTime, yesterdayEnd);
    
    const yesterdayResult = await yesterdayRequest.query(todayQuery);
    const yesterdayData = yesterdayResult.recordset[0];

    console.log('Yesterday\'s Results:');
    console.log('  Transactions:', yesterdayData.TransactionCount.toLocaleString());
    console.log('  Revenue: ₹' + yesterdayData.TotalRevenue.toLocaleString());
    console.log('  Plazas:', yesterdayData.PlazaCount);
    console.log('  Vehicles:', yesterdayData.VehicleCount.toLocaleString());
    console.log('  Avg Duration:', Math.round(yesterdayData.AvgDuration || 0), 'minutes');
    console.log('  Date Range:', yesterdayData.EarliestTransaction?.toISOString() || 'No data', 'to', yesterdayData.LatestTransaction?.toISOString() || 'No data');
    console.log();

    // Test payment method breakdown for today
    console.log('💳 PAYMENT METHOD BREAKDOWN (Today):');
    const paymentQuery = `
      SELECT 
        PaymentMode,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue
      FROM tblParkwiz_Parking_Data WITH (NOLOCK)
      WHERE ExitDateTime >= @startDate AND ExitDateTime <= @endDate
      GROUP BY PaymentMode
      ORDER BY TotalRevenue DESC
    `;

    const paymentRequest = new sql.Request();
    paymentRequest.input('startDate', sql.DateTime, todayStart);
    paymentRequest.input('endDate', sql.DateTime, todayEnd);
    
    const paymentResult = await paymentRequest.query(paymentQuery);
    
    paymentResult.recordset.forEach(payment => {
      console.log(`  ${payment.PaymentMode || 'Unknown'}: ${payment.TransactionCount.toLocaleString()} transactions, ₹${payment.TotalRevenue.toLocaleString()}`);
    });
    console.log();

    // Test plaza breakdown for today
    console.log('🏢 TOP PLAZAS (Today):');
    const plazaQuery = `
      SELECT TOP 5
        PlazaName,
        PlazaCode,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue
      FROM tblParkwiz_Parking_Data WITH (NOLOCK)
      WHERE ExitDateTime >= @startDate AND ExitDateTime <= @endDate
      GROUP BY PlazaName, PlazaCode
      ORDER BY TotalRevenue DESC
    `;

    const plazaRequest = new sql.Request();
    plazaRequest.input('startDate', sql.DateTime, todayStart);
    plazaRequest.input('endDate', sql.DateTime, todayEnd);
    
    const plazaResult = await plazaRequest.query(plazaQuery);
    
    plazaResult.recordset.forEach(plaza => {
      console.log(`  ${plaza.PlazaName} (${plaza.PlazaCode}): ${plaza.TransactionCount.toLocaleString()} transactions, ₹${plaza.TotalRevenue.toLocaleString()}`);
    });
    console.log();

    // Calculate trend (today vs yesterday)
    const revenueTrend = yesterdayData.TotalRevenue > 0 ? 
      ((todayData.TotalRevenue - yesterdayData.TotalRevenue) / yesterdayData.TotalRevenue * 100) : 0;
    const transactionTrend = yesterdayData.TransactionCount > 0 ? 
      ((todayData.TransactionCount - yesterdayData.TransactionCount) / yesterdayData.TransactionCount * 100) : 0;

    console.log('📈 TRENDS (Today vs Yesterday):');
    console.log(`  Revenue Trend: ${revenueTrend >= 0 ? '+' : ''}${revenueTrend.toFixed(2)}%`);
    console.log(`  Transaction Trend: ${transactionTrend >= 0 ? '+' : ''}${transactionTrend.toFixed(2)}%`);
    console.log();

    // Test recent transactions
    console.log('🕐 RECENT TRANSACTIONS (Last 5):');
    const recentQuery = `
      SELECT TOP 5
        VehicleNumber,
        PlazaName,
        ExitDateTime,
        PaymentMode,
        ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0) as TotalAmount
      FROM tblParkwiz_Parking_Data WITH (NOLOCK)
      WHERE ExitDateTime >= @startDate AND ExitDateTime <= @endDate
      ORDER BY ExitDateTime DESC
    `;

    const recentRequest = new sql.Request();
    recentRequest.input('startDate', sql.DateTime, todayStart);
    recentRequest.input('endDate', sql.DateTime, todayEnd);
    
    const recentResult = await recentRequest.query(recentQuery);
    
    recentResult.recordset.forEach(transaction => {
      console.log(`  ${transaction.VehicleNumber || 'Unknown'} at ${transaction.PlazaName} - ₹${transaction.TotalAmount.toFixed(2)} (${transaction.PaymentMode}) - ${transaction.ExitDateTime.toISOString()}`);
    });

    await sql.close();
    
    console.log();
    console.log('✅ Dashboard data fetching test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing dashboard data:', error);
    if (sql.connected) {
      await sql.close();
    }
  }
}

testDashboardData();