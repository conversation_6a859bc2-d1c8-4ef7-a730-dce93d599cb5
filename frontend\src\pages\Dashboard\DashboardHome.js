import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/authContext';
import { DashboardCard } from '../../components/DashboardCard';
import { Chart } from '../../components/Chart';

import { AdvancedRevenueChart } from '../../components/AdvancedRevenueChart';
import { AdvancedPaymentMethodChart } from '../../components/AdvancedPaymentMethodChart';
import { DateRangePicker } from '../../components/DateRangePicker';
import { EntityFilter } from '../../components/EntityFilter';
import { formatCurrency, formatNumber, formatDuration } from '../../utils/formatters';
import {
  CreditCard,
  Clock,
  Car,
  Activity,
  Filter
} from 'lucide-react';
import dashboardApi from '../../api/dashboardApi';

// Import skeleton loaders and progressive loading hook
import {
  DashboardCardSkeleton,
  ChartSkeleton,
  PaymentMethodChartSkeleton,
  FilterSkeleton,
  LoadingSpinner,
  LoadingOverlay
} from '../../components/SkeletonLoaders';
import { useProgressiveLoading, COMPONENT_IDS } from '../../hooks/useProgressiveLoading';

/**
 * DashboardHome Component
 * 
 * Main dashboard page showing key metrics and visualizations
 * based on user role and permissions
 */
const DashboardHome = () => {
  const { user, isSuperAdmin, isCompanyAdmin } = useAuth();
  const [dateRange, setDateRange] = useState('today');
  const [selectedEntity, setSelectedEntity] = useState({});
  const [dashboardData, setDashboardData] = useState({
    summary: {},
    revenueByPaymentMethod: [],
    recentTransactions: [],
    peakHours: [],
    dailyRevenue: [],
    loading: true,
    error: null
  });

  // Progressive loading hook
  const {
    setComponentLoading,
    isComponentLoading,
    isAnyLoading,
    overallProgress,
    isInitialLoad,
    completeInitialLoad,
    resetLoading
  } = useProgressiveLoading();

  // Fetch dashboard data when filters change
  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, selectedEntity]);

  // Function to fetch all dashboard data with progressive loading
  const fetchDashboardData = async () => {
    try {
      // Reset loading states and start progressive loading
      resetLoading();
      setDashboardData(prev => ({ ...prev, loading: true, error: null }));

      // Set initial loading states for all components
      setComponentLoading(COMPONENT_IDS.FILTERS, false); // Filters load immediately
      setComponentLoading(COMPONENT_IDS.SUMMARY_CARDS, true);
      setComponentLoading(COMPONENT_IDS.REVENUE_CHART, true);
      setComponentLoading(COMPONENT_IDS.PAYMENT_CHART, true);

      // Initialize an object to store our data as it comes in
      const newData = {};

      // Use Promise.all to fetch data in parallel but handle individual failures
      const fetchPromises = [
        // Fetch summary data (highest priority)
        dashboardApi.getDashboardSummary({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.summary = response.data.data;
          // Update state as soon as summary data is available
          setDashboardData(prev => ({
            ...prev,
            summary: response.data.data,
            loading: false
          }));
          // Mark summary cards as loaded
          setComponentLoading(COMPONENT_IDS.SUMMARY_CARDS, false);
        })
        .catch(error => {
          console.error('Error fetching summary data:', error);
          newData.summaryError = 'Failed to load summary data';
          setComponentLoading(COMPONENT_IDS.SUMMARY_CARDS, false);
        }),
        
        // Fetch recent transactions (medium priority)
        dashboardApi.getRecentTransactions({
          ...selectedEntity,
          limit: 5
        })
        .then(response => {
          newData.recentTransactions = response.data.data;
          // Update state as soon as transaction data is available
          setDashboardData(prev => ({
            ...prev,
            recentTransactions: response.data.data
          }));
        })
        .catch(error => {
          console.error('Error fetching recent transactions:', error);
          newData.transactionsError = 'Failed to load recent transactions';
        }),

        // Fetch revenue by payment method (lower priority)
        dashboardApi.getRevenueByPaymentMethod({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.revenueByPaymentMethod = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            revenueByPaymentMethod: response.data.data
          }));
          // Mark payment chart as loaded
          setComponentLoading(COMPONENT_IDS.PAYMENT_CHART, false);
        })
        .catch(error => {
          console.error('Error fetching payment method data:', error);
          newData.paymentMethodError = 'Failed to load payment method data';
          setComponentLoading(COMPONENT_IDS.PAYMENT_CHART, false);
        }),
        
        // Fetch peak hours data (lowest priority)
        dashboardApi.getPeakHoursData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.peakHours = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            peakHours: response.data.data
          }));
        })
        .catch(error => {
          console.error('Error fetching peak hours data:', error);
          newData.peakHoursError = 'Failed to load peak hours data';
        }),

        // Fetch daily revenue data (for transaction overview chart)
        dashboardApi.getDailyRevenueData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.dailyRevenue = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            dailyRevenue: response.data.data
          }));
          // Mark revenue chart as loaded
          setComponentLoading(COMPONENT_IDS.REVENUE_CHART, false);
        })
        .catch(error => {
          console.error('Error fetching daily revenue data:', error);
          newData.dailyRevenueError = 'Failed to load daily revenue data';
          setComponentLoading(COMPONENT_IDS.REVENUE_CHART, false);
        })
      ];

      // Wait for all promises to settle (either resolve or reject)
      await Promise.allSettled(fetchPromises);

      // Final update to ensure all data is in sync and loading state is false
      setDashboardData(prev => ({
        ...prev,
        ...newData,
        loading: false,
        // Only set overall error if all requests failed
        error: Object.keys(newData).length === 0 ? 'Failed to load dashboard data' : null
      }));

      // Complete initial load after a short delay for smooth UX
      setTimeout(() => {
        completeInitialLoad();
      }, 500);
      
    } catch (error) {
      console.error('Error in dashboard data fetching:', error);
      setDashboardData(prev => ({ 
        ...prev, 
        loading: false, 
        error: 'Failed to load dashboard data' 
      }));
    }
  };



  // Show loading overlay for initial load
  if (isInitialLoad && dashboardData.loading) {
    return (
      <LoadingOverlay
        message="Loading Dashboard..."
        progress={overallProgress}
      />
    );
  }

  // Render error state
  if (dashboardData.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{dashboardData.error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
        </div>
        
        {/* Filters Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <div className="flex flex-wrap gap-3 items-center">
              <DateRangePicker value={dateRange} onChange={setDateRange} />
              {(isSuperAdmin() || isCompanyAdmin()) && (
                <EntityFilter 
                  userRole={user?.role} 
                  selectedEntity={selectedEntity} 
                  onChange={setSelectedEntity} 
                />
              )}
              {/* Debug user role - hidden */}
              <div style={{ display: 'none' }}>
                User role: {user?.role || 'undefined'}, 
                isSuperAdmin: {isSuperAdmin() ? 'true' : 'false'}, 
                isCompanyAdmin: {isCompanyAdmin() ? 'true' : 'false'}
              </div>
            </div>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {isComponentLoading(COMPONENT_IDS.SUMMARY_CARDS) ? (
            // Show skeleton loaders while loading
            <>
              <DashboardCardSkeleton />
              <DashboardCardSkeleton />
              <DashboardCardSkeleton />
              <DashboardCardSkeleton />
            </>
          ) : (
            // Show actual cards when loaded
            <>
              <div className="animate-fadeInUp">
                <DashboardCard
                  title="Total Revenue"
                  value={formatCurrency(dashboardData.summary.totalRevenue || 0)}
                  trend={dashboardData.summary.revenueTrend}
                  icon={CreditCard}
                  color="bg-blue-500"
                />
              </div>
              <div className="animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
                <DashboardCard
                  title="Total Transactions"
                  value={formatNumber(dashboardData.summary.transactionCount || 0)}
                  trend={dashboardData.summary.transactionTrend}
                  icon={Activity}
                  color="bg-green-500"
                />
              </div>
              <div className="animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
                <DashboardCard
                  title="Vehicles Processed"
                  value={formatNumber(dashboardData.summary.vehicleCount || 0)}
                  trend={dashboardData.summary.vehicleTrend}
                  icon={Car}
                  color="bg-purple-500"
                />
              </div>
              <div className="animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
                <DashboardCard
                  title="Avg. Parking Duration"
                  value={formatDuration(dashboardData.summary.avgDuration)}
                  trend={dashboardData.summary.durationTrend}
                  icon={Clock}
                  color="bg-yellow-500"
                />
              </div>
            </>
          )}
        </div>

        {/* Advanced Daily Revenue Overview - Full Width */}
        <div className="w-full">
          {isComponentLoading(COMPONENT_IDS.REVENUE_CHART) ? (
            <ChartSkeleton height={450} title="Daily Revenue Overview" />
          ) : (
            <div className="animate-fadeInUp">
              <AdvancedRevenueChart
                data={dashboardData.dailyRevenue}
                title="Daily Revenue Overview"
                height={450}
                showAnimation={true}
                dateRange={dateRange}
              />
            </div>
          )}
        </div>

        {/* Advanced Payment Method Distribution */}
        {isComponentLoading(COMPONENT_IDS.PAYMENT_CHART) ? (
          <PaymentMethodChartSkeleton />
        ) : (
          <div className="animate-slideInRight">
            <AdvancedPaymentMethodChart
              data={dashboardData.revenueByPaymentMethod || []}
              title="Revenue by Payment Method"
              showRefresh={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};



export default DashboardHome;
