import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/authContext';
import { DashboardCard } from '../../components/DashboardCard';
import { Chart } from '../../components/Chart';
import { D3Chart } from '../../components/D3Chart';
import { AdvancedRevenueChart } from '../../components/AdvancedRevenueChart';
import { DateRangePicker } from '../../components/DateRangePicker';
import { EntityFilter } from '../../components/EntityFilter';
import { formatCurrency, formatNumber, formatDuration } from '../../utils/formatters';
import {
  CreditCard,
  Clock,
  Car,
  Activity,
  Filter
} from 'lucide-react';
import dashboardApi from '../../api/dashboardApi';

/**
 * DashboardHome Component
 * 
 * Main dashboard page showing key metrics and visualizations
 * based on user role and permissions
 */
const DashboardHome = () => {
  const { user, isSuperAdmin, isCompanyAdmin } = useAuth();
  const [dateRange, setDateRange] = useState('today');
  const [selectedEntity, setSelectedEntity] = useState({});
  const [dashboardData, setDashboardData] = useState({
    summary: {},
    revenueByPaymentMethod: [],
    recentTransactions: [],
    peakHours: [],
    dailyRevenue: [],
    loading: true,
    error: null
  });

  // Fetch dashboard data when filters change
  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, selectedEntity]);

  // Function to fetch all dashboard data with progressive loading
  const fetchDashboardData = async () => {
    try {
      setDashboardData(prev => ({ ...prev, loading: true }));
      
      // Initialize an object to store our data as it comes in
      const newData = {};
      
      // Use Promise.all to fetch data in parallel but handle individual failures
      const fetchPromises = [
        // Fetch summary data (highest priority)
        dashboardApi.getDashboardSummary({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.summary = response.data.data;
          // Update state as soon as summary data is available
          setDashboardData(prev => ({ 
            ...prev, 
            summary: response.data.data,
            loading: false 
          }));
        })
        .catch(error => {
          console.error('Error fetching summary data:', error);
          newData.summaryError = 'Failed to load summary data';
        }),
        
        // Fetch recent transactions (medium priority)
        dashboardApi.getRecentTransactions({
          ...selectedEntity,
          limit: 5
        })
        .then(response => {
          newData.recentTransactions = response.data.data;
          // Update state as soon as transaction data is available
          setDashboardData(prev => ({ 
            ...prev, 
            recentTransactions: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching recent transactions:', error);
          newData.transactionsError = 'Failed to load recent transactions';
        }),
        
        // Fetch revenue by payment method (lower priority)
        dashboardApi.getRevenueByPaymentMethod({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.revenueByPaymentMethod = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({ 
            ...prev, 
            revenueByPaymentMethod: response.data.data 
          }));
        })
        .catch(error => {
          console.error('Error fetching payment method data:', error);
          newData.paymentMethodError = 'Failed to load payment method data';
        }),
        
        // Fetch peak hours data (lowest priority)
        dashboardApi.getPeakHoursData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.peakHours = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            peakHours: response.data.data
          }));
        })
        .catch(error => {
          console.error('Error fetching peak hours data:', error);
          newData.peakHoursError = 'Failed to load peak hours data';
        }),

        // Fetch daily revenue data (for transaction overview chart)
        dashboardApi.getDailyRevenueData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.dailyRevenue = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            dailyRevenue: response.data.data
          }));
        })
        .catch(error => {
          console.error('Error fetching daily revenue data:', error);
          newData.dailyRevenueError = 'Failed to load daily revenue data';
        })
      ];
      
      // Wait for all promises to settle (either resolve or reject)
      await Promise.allSettled(fetchPromises);
      
      // Final update to ensure all data is in sync and loading state is false
      setDashboardData(prev => ({ 
        ...prev,
        ...newData,
        loading: false,
        // Only set overall error if all requests failed
        error: Object.keys(newData).length === 0 ? 'Failed to load dashboard data' : null
      }));
      
    } catch (error) {
      console.error('Error in dashboard data fetching:', error);
      setDashboardData(prev => ({ 
        ...prev, 
        loading: false, 
        error: 'Failed to load dashboard data' 
      }));
    }
  };

  // Get color based on payment method - Golden theme colors
  const getPaymentMethodColor = (method) => {
    switch(method?.toLowerCase()) {
      case 'cash': return 'bg-emerald-500';
      case 'fastag': return 'bg-amber-500';
      case 'upi': return 'bg-orange-500';
      case 'card': return 'bg-yellow-500';
      default: return 'bg-amber-600';
    }
  };

  // Render loading state
  if (dashboardData.loading) {
    return (
      <div className="p-6 flex justify-center items-center h-64">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-200 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-blue-200 rounded mb-2"></div>
          <div className="h-3 w-24 bg-blue-100 rounded"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (dashboardData.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{dashboardData.error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
        </div>
        
        {/* Filters Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <div className="flex flex-wrap gap-3 items-center">
              <DateRangePicker value={dateRange} onChange={setDateRange} />
              {(isSuperAdmin() || isCompanyAdmin()) && (
                <EntityFilter 
                  userRole={user?.role} 
                  selectedEntity={selectedEntity} 
                  onChange={setSelectedEntity} 
                />
              )}
              {/* Debug user role - hidden */}
              <div style={{ display: 'none' }}>
                User role: {user?.role || 'undefined'}, 
                isSuperAdmin: {isSuperAdmin() ? 'true' : 'false'}, 
                isCompanyAdmin: {isCompanyAdmin() ? 'true' : 'false'}
              </div>
            </div>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <DashboardCard
            title="Total Revenue"
            value={formatCurrency(dashboardData.summary.totalRevenue || 0)}
            trend={dashboardData.summary.revenueTrend}
            icon={CreditCard}
            color="bg-blue-500" 
          />
          <DashboardCard
            title="Total Transactions"
            value={formatNumber(dashboardData.summary.transactionCount || 0)}
            trend={dashboardData.summary.transactionTrend}
            icon={Activity}
            color="bg-green-500" 
          />
          <DashboardCard
            title="Vehicles Processed"
            value={formatNumber(dashboardData.summary.vehicleCount || 0)}
            trend={dashboardData.summary.vehicleTrend}
            icon={Car}
            color="bg-purple-500" 
          />
          <DashboardCard
            title="Avg. Parking Duration"
            value={formatDuration(dashboardData.summary.avgDuration)}
            trend={dashboardData.summary.durationTrend}
            icon={Clock}
            color="bg-yellow-500" 
          />
        </div>

        {/* Advanced Daily Revenue Overview - Full Width */}
        <div className="w-full">
          <AdvancedRevenueChart
            data={dashboardData.dailyRevenue}
            title="Daily Revenue Overview"
            height={450}
            showAnimation={true}
            dateRange={dateRange}
          />
        </div>

        {/* Payment Method Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">Payment Method Distribution</h2>
          {dashboardData.revenueByPaymentMethod && dashboardData.revenueByPaymentMethod.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* D3 Pie Chart */}
              <div>
                <D3Chart
                  type="pie"
                  data={dashboardData.revenueByPaymentMethod.map(method => ({
                    label: method.paymentMode || 'Unknown',
                    value: method.totalRevenue,
                    count: method.transactionCount
                  }))}
                  options={{
                    height: 300,
                    xKey: "label",
                    yKey: "value",
                    donut: true,
                    showLegend: true
                  }}
                />
              </div>
              
              {/* Payment Method Cards */}
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-3">Payment Details</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {dashboardData.revenueByPaymentMethod.map((method, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-md ${getPaymentMethodColor(method.paymentMode)}`}>
                          <CreditCard className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="font-medium">{method.paymentMode || 'Unknown'}</h3>
                      </div>
                      <p className="text-lg sm:text-xl lg:text-2xl font-bold">
                        {formatCurrency(method.totalRevenue)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatNumber(method.transactionCount)} transactions
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center h-32 text-gray-500">
              No payment data available for the selected period
            </div>
          )}
        </div>
      </div>
    </div>
  );
};



export default DashboardHome;
