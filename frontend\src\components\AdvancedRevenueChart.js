import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { useTheme } from "../contexts/themeContext";

/**
 * AdvancedRevenueChart Component
 * 
 * A sophisticated D3.js line chart component for displaying daily revenue data
 * with interactive features, animations, and smooth curves similar to the Next.js example.
 * 
 * Features:
 * - Smooth line chart with area gradient
 * - Interactive tooltips with detailed information
 * - Animation controls (play/reset)
 * - Responsive design
 * - Theme support
 * - Grid lines and proper axes
 */
export function AdvancedRevenueChart({
  data = [],
  title = "Daily Revenue Overview",
  height = 400,
  showAnimation = true,
  dateRange = 'today'
}) {
  const svgRef = useRef(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentDataIndex, setCurrentDataIndex] = useState(data?.length || 0);
  const { theme } = useTheme();

  // Animation functions
  const animateChart = () => {
    if (isAnimating || !data || data.length === 0) return;

    setIsAnimating(true);
    setCurrentDataIndex(1);

    const interval = setInterval(() => {
      setCurrentDataIndex(prev => {
        if (prev >= data.length) {
          clearInterval(interval);
          setIsAnimating(false);
          return data.length;
        }
        return prev + 1;
      });
    }, 100);
  };

  const resetChart = () => {
    if (!data) return;
    setCurrentDataIndex(data.length);
    setIsAnimating(false);
  };

  // Define theme-based colors with golden yellow theme
  const getThemeColors = () => {
    switch(theme) {
      case 'dark':
        return {
          primary: '#fbbf24', // golden yellow-400
          secondary: '#f59e0b', // amber-500
          background: '#1f2937', // gray-800
          text: '#f9fafb', // gray-50
          grid: '#4b5563', // gray-600
          tooltip: {
            background: 'rgba(251, 191, 36, 0.95)',
            text: '#1f2937'
          }
        };
      case 'saffron':
        return {
          primary: '#f59e0b', // amber-500
          secondary: '#fbbf24', // yellow-400
          background: '#fffbeb', // yellow-50
          text: '#78350f', // yellow-900
          grid: '#d97706', // yellow-600
          tooltip: {
            background: 'rgba(245, 158, 11, 0.95)',
            text: '#fffbeb'
          }
        };
      default: // light with golden theme
        return {
          primary: '#f59e0b', // amber-500 (golden yellow)
          secondary: '#fbbf24', // yellow-400
          background: '#ffffff', // white
          text: '#1f2937', // gray-800
          grid: '#e5e7eb', // gray-200
          tooltip: {
            background: 'rgba(245, 158, 11, 0.95)',
            text: 'white'
          }
        };
    }
  };

  // Auto-load animation for week and month date ranges
  useEffect(() => {
    if (data && data.length > 0 && (dateRange === 'week' || dateRange === 'month')) {
      // Auto-start animation for week/month views
      setTimeout(() => {
        if (!isAnimating) {
          animateChart();
        }
      }, 500);
    } else if (data && data.length > 0) {
      // For other date ranges, show full data immediately
      setCurrentDataIndex(data.length);
    }
  }, [data, dateRange]);

  useEffect(() => {
    if (!svgRef.current || !data || data.length === 0) return;

    const colors = getThemeColors();
    
    // Clear previous chart
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // Responsive margins based on screen size
    const containerWidth = svgRef.current.clientWidth;
    const margin = containerWidth < 768
      ? { top: 20, right: 40, bottom: 50, left: 50 }  // Mobile
      : { top: 30, right: 100, bottom: 60, left: 80 }; // Desktop

    const width = containerWidth - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g").attr("transform", `translate(${margin.left},${margin.top})`);

    // Create scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => new Date(d.date)))
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.revenue) * 1.1])
      .range([chartHeight, 0]);

    // Line generator
    const line = d3.line()
      .x(d => xScale(new Date(d.date)))
      .y(d => yScale(d.revenue))
      .curve(d3.curveCardinal);

    // Area generator
    const area = d3.area()
      .x(d => xScale(new Date(d.date)))
      .y0(chartHeight)
      .y1(d => yScale(d.revenue))
      .curve(d3.curveCardinal);

    // Gradient definition
    const defs = svg.append("defs");
    const gradient = defs.append("linearGradient")
      .attr("id", "revenueGradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0)
      .attr("y1", chartHeight)
      .attr("x2", 0)
      .attr("y2", 0);

    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", colors.primary)
      .attr("stop-opacity", 0.1);

    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", colors.primary)
      .attr("stop-opacity", 0.8);

    // Add axes
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%b %d")))
      .selectAll("text")
        .style("fill", colors.text);

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `₹${((d) / 1000).toFixed(0)}k`))
      .selectAll("text")
        .style("fill", colors.text);

    // Add grid lines
    g.append("g")
      .attr("class", "grid")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickSize(-chartHeight)
        .tickFormat(""))
      .style("stroke-dasharray", "3,3")
      .style("opacity", 0.3)
      .style("stroke", colors.grid);

    g.append("g")
      .attr("class", "grid")
      .call(d3.axisLeft(yScale)
        .tickSize(-width)
        .tickFormat(""))
      .style("stroke-dasharray", "3,3")
      .style("opacity", 0.3)
      .style("stroke", colors.grid);

    // Current data slice for animation
    const currentData = data.slice(0, currentDataIndex);

    // Add the area
    const areaPath = g.append("path")
      .datum(currentData)
      .attr("fill", "url(#revenueGradient)")
      .attr("d", area);

    // Add the line
    const linePath = g.append("path")
      .datum(currentData)
      .attr("fill", "none")
      .attr("stroke", colors.primary)
      .attr("stroke-width", 3)
      .attr("d", line);

    // Add tooltip
    const tooltip = d3.select("body")
      .append("div")
      .attr("class", "d3-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background", colors.tooltip.background)
      .style("color", colors.tooltip.text)
      .style("padding", "10px")
      .style("border-radius", "5px")
      .style("font-size", "12px")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

    // Add data points with hover effects
    const dots = g.selectAll(".dot")
      .data(currentData)
      .enter()
      .append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(new Date(d.date)))
      .attr("cy", d => yScale(d.revenue))
      .attr("r", 4)
      .attr("fill", colors.primary)
      .attr("stroke", colors.background)
      .attr("stroke-width", 2)
      .style("cursor", "pointer");

    dots.on("mouseover", function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("r", 6);

        tooltip.style("visibility", "visible")
          .html(`
            <strong>Date:</strong> ${new Date(d.date).toLocaleDateString()}<br/>
            <strong>Revenue:</strong> ₹${d.revenue.toLocaleString()}<br/>
            <strong>Transactions:</strong> ${d.transactions.toLocaleString()}<br/>
            <strong>Avg Revenue:</strong> ₹${d.avgRevenue.toFixed(2)}
          `);
      })
      .on("mousemove", (event) => {
        tooltip.style("top", (event.pageY - 10) + "px")
          .style("left", (event.pageX + 10) + "px");
      })
      .on("mouseout", function() {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("r", 4);

        tooltip.style("visibility", "hidden");
      });

    // Legend - responsive positioning
    const legendX = containerWidth < 768 ? 10 : width - 100;
    const legendY = containerWidth < 768 ? chartHeight + 30 : 20;

    const legend = g.append("g")
      .attr("transform", `translate(${legendX}, ${legendY})`);

    legend.append("line")
      .attr("x1", 0)
      .attr("x2", 20)
      .attr("y1", 0)
      .attr("y2", 0)
      .attr("stroke", colors.primary)
      .attr("stroke-width", 3);

    legend.append("text")
      .attr("x", 25)
      .attr("y", 0)
      .attr("dy", "0.35em")
      .style("font-size", containerWidth < 768 ? "11px" : "12px")
      .style("fill", colors.text)
      .text("Daily Revenue");

    // Cleanup tooltip on unmount
    return () => {
      d3.select(".d3-tooltip").remove();
    };
  }, [data, currentDataIndex, theme, height]);

  // If no data, show a placeholder
  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 border border-amber-100">
        <h2 className="text-lg md:text-xl font-semibold text-gray-700 flex items-center mb-4">
          <div className="w-1 h-6 bg-amber-500 rounded-full mr-3"></div>
          {title}
        </h2>
        <div className="flex flex-col justify-center items-center h-64 text-gray-500 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-amber-400 mb-4">
            <path d="M3 3v18h18"/>
            <path d="m19 9-5 5-4-4-3 3"/>
          </svg>
          <p className="text-center">No revenue data available for the selected period</p>
          <p className="text-sm text-gray-400 mt-2">Try selecting a different date range</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 border border-amber-100">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
        <h2 className="text-lg md:text-xl font-semibold text-gray-700 flex items-center">
          <div className="w-1 h-6 bg-amber-500 rounded-full mr-3"></div>
          {title}
        </h2>
        {showAnimation && (
          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 text-sm border border-amber-300 rounded-md flex items-center gap-1 hover:bg-amber-50 hover:border-amber-400 transition-colors duration-200 disabled:opacity-50"
              onClick={animateChart}
              disabled={isAnimating}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              <span className="hidden sm:inline">Animate</span>
            </button>
            <button
              className="px-3 py-1 text-sm border border-amber-300 rounded-md flex items-center gap-1 hover:bg-amber-50 hover:border-amber-400 transition-colors duration-200"
              onClick={resetChart}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 2v6h6"></path>
                <path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path>
                <path d="M21 22v-6h-6"></path>
                <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path>
              </svg>
              <span className="hidden sm:inline">Reset</span>
            </button>
          </div>
        )}
      </div>
      <div className="w-full overflow-x-auto bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg p-2">
        <svg
          ref={svgRef}
          width="100%"
          height={height}
          className="w-full"
          style={{ minWidth: "320px" }}
        />
      </div>
      <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
            <span><strong>Revenue Trend:</strong> Daily revenue performance tracking</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-amber-400 rounded-full"></div>
            <span><strong>Transaction Volume:</strong> Daily transaction count analysis</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-amber-600 rounded-full"></div>
            <span><strong>Average Value:</strong> Revenue per transaction insights</span>
          </div>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          💡 <strong>Tip:</strong> Hover over data points for detailed information • Use animation controls for dynamic visualization
        </div>
      </div>
    </div>
  );
}
