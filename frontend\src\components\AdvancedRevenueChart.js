import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { useTheme } from "../contexts/themeContext";

/**
 * AdvancedRevenueChart Component
 * 
 * A sophisticated D3.js line chart component for displaying daily revenue data
 * with interactive features, animations, and smooth curves similar to the Next.js example.
 * 
 * Features:
 * - Smooth line chart with area gradient
 * - Interactive tooltips with detailed information
 * - Animation controls (play/reset)
 * - Responsive design
 * - Theme support
 * - Grid lines and proper axes
 */
export function AdvancedRevenueChart({ 
  data = [],
  title = "Daily Revenue Overview",
  height = 400,
  showAnimation = true
}) {
  const svgRef = useRef(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentDataIndex, setCurrentDataIndex] = useState(data?.length || 0);
  const { theme } = useTheme();

  // Define theme-based colors
  const getThemeColors = () => {
    switch(theme) {
      case 'dark':
        return {
          primary: '#3b82f6', // blue-500
          secondary: '#10b981', // emerald-500
          background: '#1f2937', // gray-800
          text: '#f9fafb', // gray-50
          grid: '#4b5563', // gray-600
          tooltip: {
            background: 'rgba(255, 255, 255, 0.9)',
            text: '#1f2937'
          }
        };
      case 'saffron':
        return {
          primary: '#f97316', // orange-500
          secondary: '#eab308', // yellow-500
          background: '#fffbeb', // yellow-50
          text: '#78350f', // yellow-900
          grid: '#d97706', // yellow-600
          tooltip: {
            background: 'rgba(120, 53, 15, 0.9)',
            text: '#fffbeb'
          }
        };
      default: // light
        return {
          primary: '#3b82f6', // blue-500
          secondary: '#10b981', // emerald-500
          background: '#ffffff', // white
          text: '#1f2937', // gray-800
          grid: '#e5e7eb', // gray-200
          tooltip: {
            background: 'rgba(0, 0, 0, 0.8)',
            text: 'white'
          }
        };
    }
  };

  useEffect(() => {
    if (!svgRef.current || !data || data.length === 0) return;

    const colors = getThemeColors();
    
    // Clear previous chart
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = svgRef.current.clientWidth - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g").attr("transform", `translate(${margin.left},${margin.top})`);

    // Create scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => new Date(d.date)))
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.revenue) * 1.1])
      .range([chartHeight, 0]);

    // Line generator
    const line = d3.line()
      .x(d => xScale(new Date(d.date)))
      .y(d => yScale(d.revenue))
      .curve(d3.curveCardinal);

    // Area generator
    const area = d3.area()
      .x(d => xScale(new Date(d.date)))
      .y0(chartHeight)
      .y1(d => yScale(d.revenue))
      .curve(d3.curveCardinal);

    // Gradient definition
    const defs = svg.append("defs");
    const gradient = defs.append("linearGradient")
      .attr("id", "revenueGradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0)
      .attr("y1", chartHeight)
      .attr("x2", 0)
      .attr("y2", 0);

    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", colors.primary)
      .attr("stop-opacity", 0.1);

    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", colors.primary)
      .attr("stop-opacity", 0.8);

    // Add axes
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%b %d")))
      .selectAll("text")
        .style("fill", colors.text);

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `₹${((d) / 1000).toFixed(0)}k`))
      .selectAll("text")
        .style("fill", colors.text);

    // Add grid lines
    g.append("g")
      .attr("class", "grid")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale)
        .tickSize(-chartHeight)
        .tickFormat(""))
      .style("stroke-dasharray", "3,3")
      .style("opacity", 0.3)
      .style("stroke", colors.grid);

    g.append("g")
      .attr("class", "grid")
      .call(d3.axisLeft(yScale)
        .tickSize(-width)
        .tickFormat(""))
      .style("stroke-dasharray", "3,3")
      .style("opacity", 0.3)
      .style("stroke", colors.grid);

    // Current data slice for animation
    const currentData = data.slice(0, currentDataIndex);

    // Add the area
    const areaPath = g.append("path")
      .datum(currentData)
      .attr("fill", "url(#revenueGradient)")
      .attr("d", area);

    // Add the line
    const linePath = g.append("path")
      .datum(currentData)
      .attr("fill", "none")
      .attr("stroke", colors.primary)
      .attr("stroke-width", 3)
      .attr("d", line);

    // Add tooltip
    const tooltip = d3.select("body")
      .append("div")
      .attr("class", "d3-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background", colors.tooltip.background)
      .style("color", colors.tooltip.text)
      .style("padding", "10px")
      .style("border-radius", "5px")
      .style("font-size", "12px")
      .style("z-index", "1000")
      .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

    // Add data points with hover effects
    const dots = g.selectAll(".dot")
      .data(currentData)
      .enter()
      .append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(new Date(d.date)))
      .attr("cy", d => yScale(d.revenue))
      .attr("r", 4)
      .attr("fill", colors.primary)
      .attr("stroke", colors.background)
      .attr("stroke-width", 2)
      .style("cursor", "pointer");

    dots.on("mouseover", function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("r", 6);

        tooltip.style("visibility", "visible")
          .html(`
            <strong>Date:</strong> ${new Date(d.date).toLocaleDateString()}<br/>
            <strong>Revenue:</strong> ₹${d.revenue.toLocaleString()}<br/>
            <strong>Transactions:</strong> ${d.transactions.toLocaleString()}<br/>
            <strong>Avg Revenue:</strong> ₹${d.avgRevenue.toFixed(2)}
          `);
      })
      .on("mousemove", (event) => {
        tooltip.style("top", (event.pageY - 10) + "px")
          .style("left", (event.pageX + 10) + "px");
      })
      .on("mouseout", function() {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("r", 4);

        tooltip.style("visibility", "hidden");
      });

    // Legend
    const legend = g.append("g")
      .attr("transform", `translate(${width - 100}, 20)`);

    legend.append("line")
      .attr("x1", 0)
      .attr("x2", 20)
      .attr("y1", 0)
      .attr("y2", 0)
      .attr("stroke", colors.primary)
      .attr("stroke-width", 3);

    legend.append("text")
      .attr("x", 25)
      .attr("y", 0)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("fill", colors.text)
      .text("Revenue");

    // Cleanup tooltip on unmount
    return () => {
      d3.select(".d3-tooltip").remove();
    };
  }, [data, currentDataIndex, theme, height]);

  // Animation functions
  const animateChart = () => {
    if (isAnimating || !data || data.length === 0) return;

    setIsAnimating(true);
    setCurrentDataIndex(1);

    const interval = setInterval(() => {
      setCurrentDataIndex(prev => {
        if (prev >= data.length) {
          clearInterval(interval);
          setIsAnimating(false);
          return data.length;
        }
        return prev + 1;
      });
    }, 100);
  };

  const resetChart = () => {
    if (!data) return;
    setCurrentDataIndex(data.length);
    setIsAnimating(false);
  };

  // If no data, show a placeholder
  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">{title}</h2>
        <div className="flex justify-center items-center h-64 text-gray-500">
          No revenue data available for the selected period
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-700">{title}</h2>
        {showAnimation && (
          <div className="flex items-center space-x-2">
            <button 
              className="px-3 py-1 text-sm border rounded-md flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 dark:border-gray-600"
              onClick={animateChart} 
              disabled={isAnimating}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              Animate
            </button>
            <button 
              className="px-3 py-1 text-sm border rounded-md flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 dark:border-gray-600"
              onClick={resetChart}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 2v6h6"></path>
                <path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path>
                <path d="M21 22v-6h-6"></path>
                <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path>
              </svg>
              Reset
            </button>
          </div>
        )}
      </div>
      <div className="w-full overflow-x-auto">
        <svg 
          ref={svgRef} 
          width="100%" 
          height={height} 
          className="w-full" 
          style={{ minWidth: "500px" }}
        />
      </div>
      <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
        <p>
          🎯 <strong>Interactive Features:</strong> Hover over data points • Smooth animations • Real-time updates
        </p>
        <p>
          📊 <strong>D3.js Power:</strong> Custom gradients • Advanced curves • Dynamic tooltips
        </p>
      </div>
    </div>
  );
}
