# ===============================================================================
# PWVMS Production Environment Configuration
# ===============================================================================

# Application Configuration
NODE_ENV=production
PORT=5000
JWT_SECRET=your-secure-production-secret-key-here
SESSION_SECRET=your-super-secure-session-secret-change-this-in-production

# Frontend Configuration
FRONTEND_URL=https://your-production-domain.com
CORS_ORIGIN=https://your-production-domain.com

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=knqm rwgu fjhp ydhj

# ===============================================================================
# DATABASE CONFIGURATION
# ===============================================================================
DB_USER=hparkwiz
DB_PASSWORD=Parkwiz@2020
DB_SERVER=parkwizvms.database.windows.net
DB_NAME=ParkwizOps
DB_PORT=1433
DB_ENCRYPT=true
DB_TRUST_CERT=true
DB_TIMEOUT=30000
DB_REQUEST_TIMEOUT=30000
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_IDLE_TIMEOUT=30000

# ===============================================================================
# REDIS CONFIGURATION (for caching and sessions)
# ===============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis connection options
REDIS_RETRY_DELAY_ON_FAILOVER=100
REDIS_MAX_RETRIES_PER_REQUEST=3
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# ===============================================================================
# CACHING CONFIGURATION
# ===============================================================================
ENABLE_REDIS_CACHING=true

# Cache TTL (Time To Live) in seconds
CACHE_TTL_TODAY=60
CACHE_TTL_YESTERDAY=300
CACHE_TTL_WEEK=600
CACHE_TTL_MONTH=1800
CACHE_TTL_YEAR=3600

# ===============================================================================
# PERFORMANCE CONFIGURATION
# ===============================================================================
ENABLE_REAL_TIME_UPDATES=true
ENABLE_API_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===============================================================================
# FILE UPLOAD CONFIGURATION
# ===============================================================================
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./Uploads
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif
ALLOWED_DOCUMENT_TYPES=pdf,doc,docx,xls,xlsx

# ===============================================================================
# LOGGING CONFIGURATION
# ===============================================================================
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10485760
LOG_MAX_FILES=5

# ===============================================================================
# SECURITY CONFIGURATION
# ===============================================================================
# Password policy
MIN_PASSWORD_LENGTH=8
REQUIRE_UPPERCASE=true
REQUIRE_LOWERCASE=true
REQUIRE_NUMBERS=true
REQUIRE_SPECIAL_CHARS=true

# Session configuration
SESSION_TIMEOUT_MINUTES=480
REMEMBER_ME_DAYS=30

# ===============================================================================
# HEALTH CHECKS
# ===============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
DB_HEALTH_CHECK_TIMEOUT=5000
REDIS_HEALTH_CHECK_TIMEOUT=3000